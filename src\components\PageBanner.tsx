import Link from 'next/link';

interface PageBannerProps {
  pageTitle: string;
  pageName: string;
}

const PageBanner = ({ pageTitle, pageName }: PageBannerProps) => {
  return (
    <div className="mil-banner-sm mil-deep-bg">
      <img
        src="img/home/<USER>"
        alt="background"
        className="mil-background-image"
      />
      <div
        className="mil-deco mil-deco-accent"
        style={{ top: '47%', right: '10%', transform: 'rotate(90deg)' }}
      />
      <div className="mil-banner-content">
        <div className="mil-relative container">
          <ul className="mil-breadcrumbs mil-mb-30 text-white">
            <li>
              <Link href="/">Home</Link>
            </li>
            <li>
              <a href="#">{pageName}</a>
            </li>
          </ul>
          <h2 className="mil-uppercase text-white">{pageTitle}</h2>
        </div>
      </div>
    </div>
  );
};
export default PageBanner;
