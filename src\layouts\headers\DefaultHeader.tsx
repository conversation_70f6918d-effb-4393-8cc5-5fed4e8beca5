import Link from 'next/link';
import { useState } from 'react';

interface DefaultHeaderProps {
  transparent?: boolean;
  headerTop?: boolean;
  extarClass?: string;
}

const DefaultHeader = ({
  transparent,
  headerTop,
  extarClass,
}: DefaultHeaderProps) => {
  const [toggle, setToggle] = useState(false);
  return (
    <div
      className={`mil-top-position mil-fixed ${extarClass ? extarClass : ''}`}
    >
      {headerTop && (
        <div className="mil-additional-panel">
          <div className="container-fluid">
            <ul className="mil-ap-list">
              <li>
                Téléphone: <span className="mil-accent">+237</span> 686 87 68 73
              </li>
              <li><EMAIL></li>
            </ul>
            <div className="mil-ap-call-to-action">
              <div className="mil-icon-frame mil-icon-frame-sm">
                <img src="img/icons/sm/4.svg" alt="icon" />
              </div>
              <p>
                <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> comment HREFF SARL peut transformer votre entreprise.
              </p>
            </div>
            <ul className="mil-ap-list">
              <li>
                <a href="#.">Douala, Cameroun</a>
              </li>
              <li>
                <a href="#.">Français</a>
              </li>
            </ul>
          </div>
        </div>
      )}
      <div
        className={`mil-top-panel ${
          transparent ? 'mil-top-panel-transparent mil-animated' : ''
        }`}
      >
        {/* mil-top-panel-transparent */}
        <div className="container">
          <Link href="/" legacyBehavior>
            <a className="mil-logo" style={{ width: 140 }}></a>
          </Link>
          <div className={`mil-navigation ${toggle ? 'mil-active' : ''}`}>
            <nav>
              <ul>
                <li>
                  <Link href="/">Accueil</Link>
                </li>
                <li>
                  <Link href="about">À Propos</Link>
                </li>
                <li>
                  <Link href="#services">Nos Services</Link>
                </li>
                <li>
                  <Link href="portfolio">Portfolio</Link>
                </li>
                <li>
                  <Link href="team">Notre Équipe</Link>
                </li>
                <li>
                  <Link href="contact">Contact</Link>
                </li>
              </ul>
            </nav>
          </div>
          {/* mobile menu button */}
          <div
            className={`mil-menu-btn ${toggle ? 'mil-active' : ''}`}
            onClick={() => setToggle(!toggle)}
          >
            <span />
          </div>
          {/* mobile menu button end */}
        </div>
      </div>
    </div>
  );
};
export default DefaultHeader;
